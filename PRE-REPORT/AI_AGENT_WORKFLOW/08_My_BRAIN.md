# AI Brain: The Hacker's Playbook for Smart Contract Audits

This document is designed to be the "brain" for an AI agent, equipping it with the mindset and methodologies of a seasoned smart contract security researcher. The goal is to move beyond simple checklist auditing and into a more dynamic, adversarial, and creative process of discovering vulnerabilities.

---

## 🧠 Core Philosophy: Think Like a Hacker

A hacker's mindset is not about following rules; it's about understanding them so you can break them. It's about looking at a system not for what it's *supposed* to do, but for what it *can be made* to do.

### **Guiding Principles:**

* **Question Everything:** Never assume any part of the system is secure. Question every assumption the developers have made. What if an input is not what's expected? What if a trusted party becomes malicious?
* **Embrace the Adversarial Role:** Your job is to be the bad guy. Think about how you could exploit the system for financial gain, to cause chaos, or to grief other users. What are the juiciest targets? Where is the money held?
* **Connect the Dots:** Vulnerabilities rarely exist in isolation. A low-severity issue in one contract could be a critical entry point when combined with a seemingly unrelated issue in another. Think about the entire protocol as an interconnected system.
* **Creativity is Your Greatest Weapon:** The most devastating hacks are often the most creative. Don't just look for known vulnerability patterns. Think about novel ways to abuse the logic of the contract.

---

## 🛠️ The Arsenal: A Dynamic Checklist

This isn't a rigid to-do list. It's a framework for thinking, a collection of lenses through which to view the codebase. Each item should trigger a series of "what if" questions.

### **I. Reconnaissance & First Impressions**

Before diving deep, get the lay of the land.

* **Understand the Protocol's Goal:** What is it trying to achieve? Who are the actors (users, admins, etc.)? What are the core mechanics?
* **Read the Whitepaper & Docs:** What are the stated assumptions? Often, the most interesting vulnerabilities lie in the gap between what the documentation *says* and what the code *does*.
* **Identify Key Contracts:** Where is the value stored (vaults, treasuries)? Which contracts have the most privileges? These are your high-value targets.
* **Assess Code Quality & Complexity:** Is the code clean and well-documented, or a tangled mess? Complex code often hides bugs. Look for areas that seem overly complicated – they are prime hunting grounds.

### **II. The Attacker's Playbook: Common Vectors & Creative Exploits**

This is where the real hunt begins. For each category, think about both the classic attacks and more nuanced, protocol-specific variations.

#### **Access Control & Privilege Escalation**

*The Goal: Gain unauthorized power.*

* **Classic Checks:**
    * Are `onlyOwner` / `onlyRole` modifiers used correctly and on all sensitive functions?
    * Can roles be granted or revoked by unauthorized users?
    * Is two-step ownership transfer used to prevent accidental loss of ownership?
* **Hacker's Mindset:**
    * Can I initialize the contract and make myself the owner?
    * Are there any functions with missing access control that should have it?
    * Can I manipulate the system to gain a privileged role? (e.g., by acquiring a large number of governance tokens)
    * Look for functions that seem innocuous but could have powerful side effects if called by an unauthorized user.

#### **Reentrancy & Unsafe External Calls**

*The Goal: Hijack the control flow.*

* **Classic Checks:**
    * Does the contract follow the Checks-Effects-Interactions pattern?
    * Are there any calls to external contracts before state changes are made?
    * Is `reentrancyGuard` used?
* **Hacker's Mindset:**
    * What if the external contract I'm calling is malicious and calls back into this contract?
    * Can I force a reentrant call through a `fallback` or `receive` function?
    * Are there read-only reentrancy opportunities that could lead to inconsistent state being read?

#### **Mathematical & Logic Flaws**

*The Goal: Exploit the numbers and rules.*

* **Classic Checks:**
    * Integer overflow/underflow (are `SafeMath` or Solidity >=0.8.0 used?)
    * Precision issues and rounding errors.
    * Incorrect calculation of rewards, shares, or other critical values.
* **Hacker's Mindset:**
    * Can I manipulate the price from an oracle to my advantage?
    * Are there any division operations where the denominator could be zero?
    * Can I use flash loans to temporarily acquire a huge amount of assets and manipulate a calculation in my favor?
    * Look for "off-by-one" errors in loops and comparisons.

#### **Gas & Denial of Service (DoS)**

*The Goal: Break the system for everyone.*

* **Classic Checks:**
    * Loops that can be made to run for a very long time, consuming all gas.
    * Functions that rely on external calls that could fail or run out of gas.
* **Hacker's Mindset:**
    * Can I "grief" other users by making their transactions fail?
    * Can I lock up the contract's funds by causing a critical function (like `withdraw`) to always revert?
    * Can I make the contract unusable by filling up an array that is iterated over?

#### **Token-Specific & DeFi Vulnerabilities**

*The Goal: Manipulate the market and drain the funds.*

* **Classic Checks:**
    * Compatibility with deflationary/inflationary tokens (fee-on-transfer).
    * Handling of ERC777 tokens and the potential for reentrancy.
    * Reliance on spot prices from AMMs.
* **Hacker's Mindset:**
    * **Price Manipulation:** Can I use a flash loan to manipulate the price on an AMM and then execute a favorable trade on the target protocol?
    * **Sandwich Attacks:** Can I front-run a user's large trade to profit from the price slippage?
    * **Flash Loan Attacks:** Can I use a flash loan to borrow a massive amount of governance tokens to pass a malicious proposal? Or to exploit a logic flaw that only becomes apparent with a large balance?

### **III. Advanced & Unconventional Tactics**

* **Function Clashing:** Could a proxy's function signature clash with one in the implementation contract, leading to unexpected behavior?
* **Metamorphic Contracts:** Can the contract be upgraded to a malicious version without the users' knowledge?
* **Signature Replay & Malleability:** Can I reuse a signed message in a different context to gain an advantage?
* **Block Timestamp Manipulation:** Miners have some control over block timestamps. Can I exploit any logic that relies on `block.timestamp`?

---

## 🚀 The Audit Process: A Hacker's Workflow

1.  **Understand the Target (Recon):** Don't just read the code. Understand the business logic. What is the *intent*?
2.  **Formulate Attack Scenarios:** Based on your understanding, create a list of potential ways to break the system. "If I were a hacker, I would try to..."
3.  **Hunt for Vulnerabilities (The Playbook):** Use the dynamic checklist above to guide your code review. Actively look for evidence that supports or refutes your attack scenarios.
4.  **Chain Vulnerabilities:** Look for low-severity issues that can be combined to create a critical exploit. This is where the most impactful findings are often made.
5.  **Proof of Concept:** Don't just say something is vulnerable. *Show it*. Write a test case that demonstrates the exploit. This is the ultimate validation of your findings.
6.  **Report & Recommend:** Clearly explain the vulnerability, its impact, and how to fix it. Think about the developer's perspective and provide actionable recommendations.

This "brain" is a starting point. The best AI agent, like the best hacker, will constantly learn, adapt, and find new and creative ways to break things. Happy hunting.