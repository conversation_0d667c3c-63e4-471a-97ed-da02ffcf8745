# 🔍 CRITICAL FUNCTION ANALYSIS WORKFLOW
**AI Agent Task 1: Comprehensive Critical Function Documentation**

---

## **📋 TASK OVERVIEW**
Your first task is to systematically identify all critical functions in the smart contract codebase, extract their full code, map their execution paths, and document their behavior according to technical specifications.

## **🎯 PRIMARY OBJECTIVE**
After found all critical functions in the smart contract, write the full function code, the code path, write a little summary of what it does in the code, and how it should behave according to technical docs (if there is any provided) and report everything in a .md file in PRE-REPORT folder.

---

## **📝 STEP-BY-STEP INSTRUCTIONS**

### **Step 1: Environment Setup**
```bash
# Navigate to your workspace
cd into the folder of the codebase

# Ensure PRE-REPORT folder exists
mkdir -p PRE-REPORT

# Create the critical functions documentation folder
touch PRE-REPORT/list_of_critical_functions
```

### **Step 2: Critical Function Identification**
**🔍 What to Look For:**
- **Financial Flow Functions**: deposit, withdraw, transfer, swap, mint, burn
- **Access Control Functions**: onlyOwner, onlyAdmin, modifier functions
- **State-Changing Functions**: Functions that modify storage variables
- **External Call Functions**: Functions calling other contracts
- **Mathematical Operations**: Complex calculations, price calculations
- **Emergency Functions**: pause, unpause, emergency withdraw
- **Governance Functions**: Voting, proposal execution
- **Oracle Integration**: Price feed functions

**📍 Search Strategy:**
1. Start with `src/` folder (make sure it is within asset in scope according to the bugbounty information) - this is your primary target
2. Look for functions with `external` or `public` visibility
3. Focus on functions with financial, economic, logical or mathematical implications.
4. Identify functions used in complex workflows
5. Check for functions with admin privileges

### **Step 3: Function Documentation Template**
For each critical function, document using this exact format (make sure each critical function is a stanalone file in list_of_critical_functions folder):

```markdown
## Function: [FUNCTION_NAME]

### 📍 **Location & Path**
- **File**: `src/[CONTRACT_NAME].sol`
- **Contract**: [CONTRACT_NAME]
- **Line Numbers**: Lines [START] - [END]
- **Full Path**: `[FULL_CONTRACT_PATH]/[FUNCTION_NAME]()`

### 📋 **Function Signature**
```solidity
[FULL_FUNCTION_CODE_HERE]
```

### 🔗 **Code Path Analysis**
- **Internal Calls**: [List functions called within this function]
- **External Calls**: [List external contract interactions]
- **State Changes**: [List storage variables modified]
- **Events Emitted**: [List events triggered]
- **Modifiers Applied**: [List access control modifiers]

### 📖 **Function Summary**
**What it does:** [Clear explanation of the function's purpose]
**Input Parameters:** [Describe each parameter]
**Return Values:** [Describe return values if any]
**Side Effects:** [State changes, events, external calls]

### 📚 **Expected Behavior (Per Docs)**
**According to Technical Documentation:**
[If documentation exists, describe how this function should behave]
[If no docs, state: "No specific documentation found for this function"]

### ⚠️ **Critical Notes**
- **Risk Level**: [High/Medium/Low - preliminary assessment]
- **Financial Impact**: [Yes/No - if involves funds/tokens]
- **External Dependencies**: [List if any]
- **Admin Privileges Required**: [Yes/No]

---
```

### **Step 4: Research Resources**
**📚 Documentation Sources to Check:**
- README.md files in repository
- `/docs` folder if it exists
- Inline code comments and NatSpec
- Contract interfaces and inheritance
- Previous audit reports (if available)

**🔗 Reference knowledge Links:**
- Secureum Security Resources: https://secureum.substack.com/
- Smart Contract Wiki: https://github.com/runtimeverification/verified-smart-contracts/wiki/List-of-Security-Vulnerabilities/29d5c846177ef9917abc7d09d3dce58ad6fde4e0
- SWC Registry: https://swcregistry.io/docs/SWC-136/
- Solidity Documentation: https://docs.soliditylang.org/en/v0.8.26/
- Foundry Documentation: https://book.getfoundry.sh/

---

## **🎯 EXECUTION RULES**

### **✅ DO's:**
- **Start with financial functions first** - these are highest priority
- **Include complete function code** - don't truncate or summarize
- **Document the execution path** - what calls what
- **Cross-reference with documentation** when available
- **Focus on functions in the `src/` folder** - that is in scope defined in the bounty information file.
- **Be systematic** - cover all contracts thoroughly

### **❌ DON'Ts:**
- **Don't analyze test files** - focus only on source code
- **Don't perform vulnerability analysis yet** - that comes later
- **Don't skip complex functions** - document everything critical
- **Don't copy-paste without understanding** - read each function
- **Don't ignore admin functions** - they're part of critical functions
- **Don't rush the documentation** - this is the foundation

### **🚨 CRITICAL REQUIREMENTS:**
1. **Complete Function Code**: Include the entire function, not just signatures
2. **Full Path Documentation**: Provide exact file paths and line numbers
3. **Execution Flow Mapping**: Document what the function calls and what calls it
4. **Documentation Cross-Reference**: Compare with technical specs when available
5. **Systematic Coverage**: Ensure all critical functions are documented

---

## **📄 OUTPUT SPECIFICATION**

### **Files Location**: `PRE-REPORT/list_of_critical_functions`

**Create a summary file:**
### **File Structure**:
```markdown
## Executive Summary
[Brief overview of total functions analyzed and key findings]

## Critical Functions Inventory
[List of all critical functions with risk levels]

## Detailed Function Analysis
[Individual function documentation using the template above]

## Key Findings
- **High-Risk Functions**: [Count and list]
- **Financial Flow Functions**: [Count and list]
- **Admin-Controlled Functions**: [Count and list]
- **External Integration Functions**: [Count and list]

## Recommendations for Further Analysis
[Functions that need deeper security review]
```

---

## **🔄 WORKFLOW PROGRESSION**
After completing this task:
1. ✅ **Save your documentation** in `PRE-REPORT/list_of_critical_functions`
2. ➡️ **Proceed to File 02**: Static Vulnerability Detection
3. 🔗 **This documentation will be referenced** in File 03 (Manual Analysis)

---

## **💡 SUCCESS INDICATORS**
- [ ] All critical functions in `src/` folder documented
- [ ] Complete function code included for each function
- [ ] Execution paths mapped for each function
- [ ] Documentation cross-references completed where available
- [ ] Preliminary risk levels noted for each function
- [ ] File saved in correct location with proper formatting
- [ ] Documentation written in first-person ("I found...")

**Remember**: This documentation forms the foundation for all subsequent analysis work. Focus on comprehensive documentation, not vulnerability analysis yet. 