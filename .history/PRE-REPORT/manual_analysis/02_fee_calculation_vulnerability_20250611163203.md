# CRITICAL VULNERABILITY: Fee Calculation Discrepancy in selfLiquidate

**Severity**: HIGH
**Function**: `selfLiquidate` in `LiquidationManager.sol`
**Lines**: 189-192, 224-225
**Discovery Method**: Manual code analysis following hacker mindset approach

## Vulnerability Summary

The `selfLiquidate` function contains a critical mathematical inconsistency in fee calculations that allows users to pay significantly reduced fees while still reserving the maximum collateral amount. This creates an economic arbitrage opportunity that reduces protocol revenue.

## Technical Analysis

### Code Locations

<augment_code_snippet path="jigsaw-protocol-v1/src/LiquidationManager.sol" mode="EXCERPT">
````solidity
// Line 189-192: Initial fee calculation based on amountInMaximum
tempData.totalFeeCollateral = tempData.amountInMaximum.mulDiv(selfLiquidationFee, precision, Math.Rounding.Ceil);
tempData.totalSelfLiquidatableCollateral = tempData.amountInMaximum + tempData.totalFeeCollateral;

// Line 224-225: Final fee calculation based on actual swap amount
uint256 finalFeeCollateral = collateralUsedForSwap.mulDiv(selfLiquidationFee, precision, Math.Rounding.Ceil);
````
</augment_code_snippet>

### The Problem

1. **Initial Fee Calculation**: Fee is calculated based on `amountInMaximum` (user's maximum willing to spend)
2. **Final Fee Calculation**: Fee is calculated based on `collateralUsedForSwap` (actual amount used in swap)
3. **Discrepancy**: `collateralUsedForSwap` is typically less than `amountInMaximum`, leading to lower fees

### Mathematical Proof

```
Let:
- amountInMaximum = 1000 tokens
- collateralUsedForSwap = 800 tokens (actual swap used less due to favorable rates)
- selfLiquidationFee = 1% (100 basis points)

Initial calculation (for collateral reservation):
totalFeeCollateral = 1000 * 0.01 = 10 tokens

Final calculation (actual fee charged):
finalFeeCollateral = 800 * 0.01 = 8 tokens

User saves: 10 - 8 = 2 tokens (20% fee reduction)
```

## Attack Scenario

### Prerequisites
- User has a holding with debt to self-liquidate
- Understanding of Uniswap pricing mechanics
- Ability to set `amountInMaximum` strategically

### Attack Steps

1. **Analyze Market Conditions**: Identify when Uniswap rates are favorable (less slippage expected)
2. **Set Maximum Amount**: Set `amountInMaximum` to a high value to pass collateral checks
3. **Execute Self-Liquidation**: Call `selfLiquidate` with optimized parameters
4. **Benefit from Discrepancy**: Pay fees only on actual swap amount, not reserved amount

### Economic Impact

```
For a $100,000 self-liquidation:
- Expected fee (1%): $1,000
- Actual fee paid: ~$800 (if 20% slippage buffer unused)
- Protocol loss: $200 per transaction
- User savings: $200 per transaction
```

## Root Cause Analysis

### Design Flaw
The function attempts to:
1. Reserve collateral based on maximum possible usage (including fees)
2. Charge fees based on actual usage

This creates an inherent mismatch where users can game the system by setting high maximums while expecting lower actual usage.

### Code Flow Issue
```
1. Calculate totalFeeCollateral based on amountInMaximum
2. Reserve totalSelfLiquidatableCollateral (includes inflated fee)
3. Execute swap (uses less than maximum)
4. Calculate finalFeeCollateral based on actual usage
5. Charge lower fee while keeping higher reservation
```

## Exploitation Complexity

**Difficulty**: LOW
- No special privileges required
- Standard user function
- Predictable market conditions can be exploited
- No complex contract interactions needed

**Frequency**: HIGH
- Every self-liquidation can potentially exploit this
- Market conditions often favor lower actual usage vs maximum

## Recommended Fix

### Option 1: Consistent Fee Base (Recommended)
```solidity
// Use the same base for both calculations
uint256 estimatedCollateralUsage = _getCollateralForJUsd(_collateral, tempData.jUsdAmountToBurn, tempData.exchangeRate);
tempData.totalFeeCollateral = estimatedCollateralUsage.mulDiv(selfLiquidationFee, precision, Math.Rounding.Ceil);

// Later, use the same base for final fee
uint256 finalFeeCollateral = collateralUsedForSwap.mulDiv(selfLiquidationFee, precision, Math.Rounding.Ceil);
```

### Option 2: Pre-commit Fee Amount
```solidity
// Calculate fee upfront and commit to it
uint256 committedFee = tempData.amountInMaximum.mulDiv(selfLiquidationFee, precision, Math.Rounding.Ceil);

// Use committed fee regardless of actual swap amount
// (with appropriate refund mechanism if swap fails)
```

### Option 3: Post-swap Fee Calculation
```solidity
// Remove initial fee calculation entirely
// Calculate all fees after swap completion
// Adjust collateral reservation logic accordingly
```

## Impact Assessment

**Financial Impact**: HIGH
- Direct protocol revenue loss
- Scales with transaction volume
- Compounds over time

**User Impact**: POSITIVE (for exploiters)
- Reduced costs for self-liquidation
- Incentivizes more self-liquidations

**Protocol Impact**: HIGH
- Reduced fee collection efficiency
- Potential economic model disruption
- Trust issues if discovered by community

## Testing Strategy

### Unit Tests Needed
1. Test with various `amountInMaximum` vs actual swap ratios
2. Test with different market conditions
3. Test fee calculation consistency
4. Test edge cases (minimum/maximum values)

### Integration Tests Needed
1. Test with real Uniswap interactions
2. Test with various collateral types
3. Test with different slippage scenarios

### Proof of Concept
A PoC should demonstrate:
1. Setting high `amountInMaximum`
2. Executing swap with lower actual usage
3. Measuring fee discrepancy
4. Calculating economic impact

## Conclusion

This vulnerability represents a significant economic flaw in the protocol's fee collection mechanism. While it doesn't directly threaten user funds, it systematically reduces protocol revenue and creates unfair advantages for sophisticated users who understand the discrepancy.

The fix should prioritize consistency in fee calculation methodology while maintaining the intended user experience and slippage protection mechanisms.

**Recommendation**: Implement Option 1 (Consistent Fee Base) as it maintains the current user experience while fixing the mathematical inconsistency.
